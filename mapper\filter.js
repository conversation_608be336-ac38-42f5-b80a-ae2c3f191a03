const fs = require('fs');
const path = require('path');

// Read input args
const [,, fileName, searchTerm] = process.argv;

if (!fileName || !searchTerm) {
  console.error('Usage: node filter.js <filename> <search term>');
  process.exit(1);
}

const filePath = path.resolve(__dirname, fileName);

// Read the file and filter lines
fs.readFile(filePath, 'utf-8', (err, data) => {
  if (err) {
    console.error(`Error reading file: ${filePath}`);
    console.error(err.message);
    process.exit(1);
  }

  const lines = data.split('\n');
  const filtered = lines.filter(line => line.includes(searchTerm));

  filtered.forEach(line => console.log(line));
});
