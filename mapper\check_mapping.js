const fs = require('fs');
const path = require('path');

// Log object to handle different log levels
const log = {
    LOG_DEBUG: 2,
    LOG_INFO: 1,
    LOG_LEVEL: 1, // Default log level is INFO

    setLogLevel(level) {
        this.LOG_LEVEL = level;
        // Log the level change
        console.log(`Log level set to: ${level === this.LOG_DEBUG ? 'DEBUG' : 'INFO'}`);
    },

    debug(msg) {
        if (this.LOG_LEVEL === this.LOG_DEBUG) {
            console.log(`🔧 DEBUG: ${msg}`);
        }
    },

    info(msg) {
        if (this.LOG_LEVEL <= this.LOG_INFO) {
            console.log(`ℹ️ INFO: ${msg}`);
        }
    },

    warn(msg) {
        console.warn(`⚠️ WARNING: ${msg}`);
    },

    error(msg) {
        console.error(`❌ ERROR: ${msg}`);
    }
};

// Step 1: Read the mapping file
const loadMapping = () => {
    const mappingFilePath = path.join(__dirname, 'variable_mapping.txt');

    log.debug(`📂 Loading mapping from: ${mappingFilePath}`);

    const mappingData = fs.readFileSync(mappingFilePath, 'utf-8');
    const mapping = {};
    const lines = mappingData.split('\n');

    lines.forEach((line, index) => {
        const [origin, target] = line.split(':').map(s => s.trim());
        if (origin && target) {
            mapping[origin] = target;
            log.debug(`🔄 Mapping loaded [${index + 1}]: ${origin} → ${target}`);
        } else {
            log.debug(`❌ Skipped invalid line [${index + 1}]: "${line}"`);
        }
    });

    log.info(`✔️ Mappings loaded: ${Object.keys(mapping).length}`);
    return mapping;
};

// Step 2: Read the original CSS file
const readCssFile = (cssFilePath) => {
    log.debug(`📄 Reading original CSS file: ${cssFilePath}`);
    return fs.readFileSync(cssFilePath, 'utf-8');
};

// Step 3: Replace the variables line by line in CSS content
const replaceCssVariables = (cssContent, mapping) => {
    const lines = cssContent.split('\n');
    let totalReplacements = 0;
    let missingVariables = []; // To track variables that couldn't be replaced

    const updatedLines = lines.map((line, index) => {
        // Match CSS variables in both declarations and var() functions
        let updatedLine = line;

        // Check for variables in CSS declarations
        Object.keys(mapping).forEach(variable => {
            if (updatedLine.includes(variable)) {
                updatedLine = updatedLine.replace(new RegExp(`\\b${variable}\\b`, 'g'), mapping[variable]);
                totalReplacements++;
                log.debug(`🔄 Line ${index + 1}: Replaced ${variable} → ${mapping[variable]}`);
            }
        });

        // Check for missing variables (those that are not in the mapping)
        Object.keys(mapping).forEach(variable => {
            if (updatedLine.includes(variable) && !mapping[variable]) {
                if (!missingVariables.includes(variable)) {
                    missingVariables.push(variable);
                }
            }
        });

        return updatedLine;
    });

    // Write missing variables to a file or write an empty file if there are no missing variables
    const missingFilePath = path.join(__dirname, 'variable_mapping_missing.txt');
    if (missingVariables.length > 0) {
        fs.writeFileSync(missingFilePath, missingVariables.join('\n'), 'utf-8');
        log.warn(`🚨 ${missingVariables.length} missing variables. Please fix!`);
    } else {
        fs.writeFileSync(missingFilePath, '', 'utf-8');
        log.info('✔️ No missing variables. All mappings were successful.');
    }

    log.info(`✔️ Replacements done: ${totalReplacements}`);
    return updatedLines.join('\n');
};

// Step 4: Write the updated CSS to a new file
const writeUpdatedCss = (updatedCss, outputFilePath) => {
    fs.writeFileSync(outputFilePath, updatedCss, 'utf-8');
    log.info(`📤 CSS written to: ${outputFilePath}`);
};

// Main execution
const main = () => {
    // Check if --debug flag is present in command line arguments
    log.setLogLevel(log.LOG_INFO)
    
    const debug = process.argv.includes('--debug'); // If --debug is passed, set debug to true, else false
    if (debug) {
        log.setLogLevel(log.LOG_DEBUG);
    }

    const mapping = loadMapping();

    const originalCssFilePath = path.join(__dirname, 'origin.css');
    const cssContent = readCssFile(originalCssFilePath);

    const updatedCss = replaceCssVariables(cssContent, mapping);

    const outputCssFilePath = path.join(__dirname, 'output.css');
    writeUpdatedCss(updatedCss, outputCssFilePath);
};

main();
