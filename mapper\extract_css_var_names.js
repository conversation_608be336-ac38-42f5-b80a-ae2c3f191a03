const fs = require('fs');
const path = require('path');

// Check if a file path is provided
const inputFile = process.argv[2];
if (!inputFile) {
  console.error('Usage: node extract.js input.css > vars.txt');
  process.exit(1);
}

// Read the file
const css = fs.readFileSync(path.resolve(inputFile), 'utf8');

// Extract CSS variable names
const variableLines = css
  .split('\n')
  .map(line => line.trim())
  .filter(line => line.startsWith('--') && line.includes(':'))
  .map(line => line.split(':')[0]);

// Output to stdout
console.log(variableLines.join('\n'));
