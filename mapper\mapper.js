const fs = require('fs');
const path = require('path');

// Log object to handle different log levels
const log = {
    LOG_DEBUG: 2,
    LOG_INFO: 1,
    LOG_LEVEL: 1, // Default log level is INFO

    setLogLevel(level) {
        this.LOG_LEVEL = level;
        // Log the level change
        console.log(`Log level set to: ${level === this.LOG_DEBUG ? 'DEBUG' : 'INFO'}`);
    },

    debug(msg) {
        if (this.LOG_LEVEL === this.LOG_DEBUG) {
            console.log(`🔧 DEBUG: ${msg}`);
        }
    },

    info(msg) {
        if (this.LOG_LEVEL <= this.LOG_INFO) {
            console.log(`ℹ️ INFO: ${msg}`);
        }
    },

    warn(msg) {
        console.warn(`⚠️ WARNING: ${msg}`);
    },

    error(msg) {
        console.error(`❌ ERROR: ${msg}`);
    }
};

// Step 1: Read the mapping file
const loadMapping = () => {
    const mappingFilePath = path.join(__dirname, 'variable_mapping.txt');
    log.debug(`📂 Loading mapping from: ${mappingFilePath}`);

    const mappingData = fs.readFileSync(mappingFilePath, 'utf-8');
    const mapping = {};
    const lines = mappingData.split('\n');
    let unsetVariables = []; // To store the unset variables for future inspection

    lines.forEach((line, index) => {
        // Remove everything after a semicolon (if present) to avoid including comments
        const lineWithoutComment = line.split(';')[0].trim();

        // Continue only if there's content after removing the comment part
        if (lineWithoutComment) {
            const [origin, target] = lineWithoutComment.split(':').map(s => s.trim());

            // If target value is "UNSET", store it in the unsetVariables array and skip it
            if (target === 'UNSET') {
                unsetVariables.push({ origin, target });
                log.debug(`❌ Skipped mapping due to UNSET value [${index + 1}]: ${origin} → ${target}`);
            } else if (origin && target) {
                mapping[origin] = target;
                log.debug(`🔄 Mapping loaded [${index + 1}]: ${origin} → ${target}`);
            } else {
                log.debug(`❌ Skipped invalid line [${index + 1}]: "${lineWithoutComment}"`);
            }
        } else {
            log.debug(`❌ Skipped empty or comment-only line [${index + 1}]`);
        }
    });

    // Write UNSET variables to uset_variable.txt for future inspection
    if (unsetVariables.length > 0) {
        const unsetFilePath = path.join(__dirname, 'uset_variable.txt');
        const unsetData = unsetVariables.map(item => `${item.origin}: ${item.target}`).join('\n');
        fs.writeFileSync(unsetFilePath, unsetData, 'utf-8');
        log.info(`✔️ UNSET variables logged to uset_variable.txt: ${unsetVariables.length}`);
    }

    log.info(`✔️ Mappings loaded: ${Object.keys(mapping).length}`);
    return mapping;
};

// Step 2: Read the original CSS file
const readCssFile = (cssFilePath) => {
    log.debug(`📄 Reading original CSS file: ${cssFilePath}`);
    return fs.readFileSync(cssFilePath, 'utf-8');
};

// Step 3: Replace the variables in CSS (line by line)
const replaceCssVariables = (cssContent, mapping) => {
    const lines = cssContent.split('\n');
    let totalReplacements = 0;
    let missingVariables = []; // To track variables that couldn't be replaced

    const updatedLines = lines.map((line, index) => {
        // General replacements for CSS properties (left-hand side variables)
        Object.keys(mapping).forEach((originVar) => {
            // Regex for CSS declarations with variables (e.g., --cta-main-nav-background-color)
            const propertyRegex = new RegExp(`(--${originVar.replace('--', '')}\\s*:|\\b${originVar}\\b)`, 'g');
            if (propertyRegex.test(line)) {
                line = line.replace(propertyRegex, (match) => {
                    // If it's a declaration (e.g., --cta-main-nav-background-color: ...), replace with the mapped value
                    if (match.includes(':')) {
                        return match.replace(originVar, mapping[originVar]);
                    } 
                    // Else, just replace the variable inside the line
                    return mapping[originVar];
                });
                log.debug(`🔄 Line ${index + 1}: Replaced ${originVar} → ${mapping[originVar]}`);
                totalReplacements++;
            }
        });

        // General replacements for variables inside var()
        Object.keys(mapping).forEach((originVar) => {
            // Regex for the var(--variable) inside values (e.g., var(--color-primary))
            const varRegex = new RegExp(`var\\(${originVar}\\)`, 'g');
            if (varRegex.test(line)) {
                line = line.replace(varRegex, `var(${mapping[originVar]})`);
                log.debug(`🔄 Line ${index + 1}: Replaced var(${originVar}) → var(${mapping[originVar]})`);
                totalReplacements++;
            }
        });

        // Handle UNSET variables (add a comment for them)
        Object.keys(mapping).forEach((originVar) => {
            if (mapping[originVar] === 'UNSET') {
                // Insert a comment for unset variables in the CSS
                const unsetComment = `/* This Variable Mapping is unset */`;
                line = line.replace(`--${originVar}`, `--${originVar}: ${mapping[originVar]}; ${unsetComment}`);
                log.debug(`🔄 Line ${index + 1}: UNSET mapping for ${originVar} added comment`);
            }
        });

        return line; // Return the updated or original line
    });

    // Handle missing variables
    const missingFilePath = path.join(__dirname, 'variable_mapping_missing.txt');
    if (missingVariables.length > 0) {
        fs.writeFileSync(missingFilePath, missingVariables.join('\n'), 'utf-8');
        log.warn(`🚨 ${missingVariables.length} missing variables. Please fix!`);
    } else {
        fs.writeFileSync(missingFilePath, '', 'utf-8');
        log.info('✔️ No missing variables. All mappings were successful.');
    }

    log.info(`✔️ Replacements done: ${totalReplacements}`);
    return updatedLines.join('\n');
};

// Step 4: Write the updated CSS to a new file
const writeUpdatedCss = (updatedCss, outputFilePath) => {
    fs.writeFileSync(outputFilePath, updatedCss, 'utf-8');
    log.info(`📤 CSS written to: ${outputFilePath}`);
};

// Main execution
const main = () => {
    // Check if --debug flag is present in command line arguments
    log.setLogLevel(log.LOG_INFO);
    
    const debug = process.argv.includes('--debug'); // If --debug is passed, set debug to true, else false
    if (debug) {
        log.setLogLevel(log.LOG_DEBUG);
    }

    const mapping = loadMapping();

    const originalCssFilePath = path.join(__dirname, 'origin.css');
    const cssContent = readCssFile(originalCssFilePath);

    const updatedCss = replaceCssVariables(cssContent, mapping);

    const outputCssFilePath = path.join(__dirname, 'output.css');
    writeUpdatedCss(updatedCss, outputCssFilePath);
};

main();
