@import url("./font-family-uxbee.css");

html[data-theme="uxbee-corporate"] {
    /* Primary Colors */
    --color-primary-1: #FFBC00;
    --color-primary-2: #FFBC00;
    --color-primary-3: #FFBC00;
    --color-primary-4: #FFBC00;

    /* Secondary Colors */
    --color-secondary-1: #252B1E;
    --color-secondary-2: var(--color-secondary-1);
    --color-secondary-3: var(--color-secondary-1);
    --color-secondary-4: var(--color-secondary-1);

    /* Tertiary Colors */
    --color-tertiary-1: #9AA899;
    --color-tertiary-2: #9AA899;
    --color-tertiary-3: var(--color-tertiary-1);
    --color-tertiary-4: var(--color-tertiary-1);

    /* Quaternary Colors */
    --color-quarternary-1: #D62839;
    --color-quaternary-1: #D62839;
    --color-quaternary-2: var(--color-quaternary-1);
    --color-quaternary-3: var(--color-quaternary-1);
    --color-quaternary-4: var(--color-quaternary-1);

    /* Additional Base Colors */
    --color-quinary: #008DD5;
    --color-senary: #FFE395;
    --color-septenary: #EBF8FF;

    /* Black Colors */
    --color-black: #000000;
    --color-black-1: #000000;
    --color-black-2: var(--color-black-1);
    --color-black-3: var(--color-black-1);
    --color-black-4: var(--color-black-1);
    --color-black-bg-1: #000000;

    /* White Colors */
    --color-white: #FFFFFF;
    --color-white-1: #FFFFFF;
    --color-white-2: var(--color-white-1);
    --color-white-3: var(--color-white-1);
    --color-white-4: var(--color-white-1);

    /* Gray Colors */
    --color-gray: #F2F2F2;
    --color-gray-1: #F2F2F2;
    --color-gray-2: var(--color-gray-1);
    --color-gray-3: var(--color-gray-1);
    --color-gray-4: var(--color-gray-1);
    --color-light-gray: #1A1A1A; /* not-found in uxbee.css */

    /* Accent Colors Group 1 */
    --color-accent1-1: #d7803f; /* not-found in uxbee.css */
    --color-accent1-2: #df9f6d; /* not-found in uxbee.css */
    --color-accent1-3: #e8bf9d; /* not-found in uxbee.css */
    --color-accent1-4: #f3dfce; /* not-found in uxbee.css */

    /* Accent Colors Group 2 */
    --color-accent2-1: #db5b45; /* not-found in uxbee.css */
    --color-accent2-2: #e08372; /* not-found in uxbee.css */
    --color-accent2-3: #e8aca1; /* not-found in uxbee.css */
    --color-accent2-4: #f2d6d0; /* not-found in uxbee.css */

    /* Accent Colors Group 3 */
    --color-accent3-1: #79c7f3; /* not-found in uxbee.css */
    --color-accent3-2: #8bd5f5; /* not-found in uxbee.css */
    --color-accent3-3: #a9e2f9; /* not-found in uxbee.css */
    --color-accent3-4: #d1f0fb; /* not-found in uxbee.css */

    /* Accent Colors Group 4 */
    --color-accent4-1: #548cbc; /* not-found in uxbee.css */
    --color-accent4-2: #72a8cc; /* not-found in uxbee.css */
    --color-accent4-3: #a9e2f9; /* not-found in uxbee.css */
    --color-accent4-4: #d1f0fb; /* not-found in uxbee.css */

    /* Accent Colors Group 5 */
    --color-accent5-1: #223c5e; /* not-found in uxbee.css */
    --color-accent5-2: #536d86; /* not-found in uxbee.css */
    --color-accent5-3: #8b9dae; /* not-found in uxbee.css */
    /* Title Colors */
    --color-title-1: var(--color-primary-1);
    --color-title-2: var(--color-tertiary-1);
    --color-title-3: var(--color-black-1);
    --color-title-4: var(--color-black-1);
    --color-title-5: var(--color-black-1);
    --color-title-6: var(--color-black-1);
    --color-title-7: var(--color-title-1); /* not-found in uxbee.css */

    /* Body Colors */
    --color-body-1: var(--color-black-1);
    --color-body-2: var(--color-body-1);
    --color-body-3: var(--color-body-1);
    --color-body-4: var(--color-body-1);
    /* Divider Colors */
    --color-dividers-1: #b9b9b9; /* not-found in uxbee.css */
    --color-dividers-2: #ececec; /* not-found in uxbee.css */
    --color-dividers-3: #f3f3f3; /* not-found in uxbee.css */
    --color-dividers-4: #f9f9f9; /* not-found in uxbee.css */

    /* Stroke Colors */
    --color-strokes-1: #e6e6e6; /* not-found in uxbee.css */
    --color-strokes-2: #606060; /* not-found in uxbee.css */
    --color-strokes-3: #f3f3f3; /* not-found in uxbee.css */
    --color-strokes-4: #f9f9f9; /* not-found in uxbee.css */
    --color-common-bg-1: #f8f8f8; /* not-found in uxbee.css */
    --color-widget-bg-1: var(--color-white-1);
    --color-link-text: var(--color-body-1);
    --color-footer-1: #1a1a1a; /* not-found in uxbee.css */
    --color-footer-2: #B1B1B1; /* not-found in uxbee.css */
    --color-alt-secondary-hover: #EBEBEB; /* not-found in uxbee.css */

    /* skin */
    /* Style */
    --btn-corners: 0.375rem;
    /* 6px */

    /* === button solid ===*/

    /* primary */
    --color-button-bg: var(--color-primary-1);
    --color-button-text: var(--color-secondary-1);
    --color-button-border: var(--color-primary-1);
    --color-button-bg-hover: var(--color-secondary-1);
    --color-button-text-hover: var(--color-primary-1);
    --color-button-border-hover: var(--color-primary-1);
    --color-button-bg-disabled: var(--color-gray-1);
    --color-button-text-disabled: var(--color-black-1);
    --color-button-border-disabled: var(--color-gray-1);

    /* secondary */
    --color-button-s-bg: var(--color-secondary-1);
    --color-button-s-text: var(--color-white-1);
    --color-button-s-border: var(--color-secondary-1);
    --color-button-s-bg-hover: var(--color-tertiary-1);
    --color-button-s-text-hover: var(--color-secondary-1);
    --color-button-s-border-hover: var(--color-secondary-1);
    --color-button-s-bg-disabled: #98BBB9;
    --color-button-s-text-disabled: #FFFFFF;
    --color-button-s-border-disabled: #98BBB9;

    /* tertiary */
    --color-button-t-bg: var(--color-tertiary-1);
    --color-button-t-text: var(--color-white-1);
    --color-button-t-border: var(--color-tertiary-1);
    --color-button-t-bg-hover: var(--color-senary);
    --color-button-t-text-hover: var(--color-secondary-1);
    --color-button-t-border-hover: var(--color-secondary-1);
    --color-button-t-bg-disabled: #A3A7DA;
    --color-button-t-text-disabled: var(--color-white-1);
    --color-button-t-border-disabled: #A3A7DA;

    /* quarternary */
    --color-button-q-bg: var(--color-quaternary-1);
    --color-button-q-text: var(--color-white-1);
    --color-button-q-border: var(--color-quaternary-1);
    --color-button-q-bg-hover: var(--color-septenary);
    --color-button-q-text-hover: var(--color-secondary-1);
    --color-button-q-border-hover: var(--color-secondary-1);
    --color-button-q-bg-disabled: #AFC8D8;
    --color-button-q-text-disabled: var(--color-white-1);
    --color-button-q-border-disabled: #AFC8D8;

    /* black */
    --color-button-b-bg: var(--color-black); /* not-found in uxbee.css */
    --color-button-b-text: var(--color-white); /* not-found in uxbee.css */
    --color-button-b-border: transparent; /* not-found in uxbee.css */
    --color-button-b-bg-hover: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-b-text-hover: var(--color-white); /* not-found in uxbee.css */
    --color-button-b-border-hover: transparent; /* not-found in uxbee.css */
    --color-button-b-bg-disabled: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-b-text-disabled: var(--color-white); /* not-found in uxbee.css */
    --color-button-b-border-disabled: transparent; /* not-found in uxbee.css */

    /* white */
    --color-button-w-bg: var(--color-white); /* not-found in uxbee.css */
    --color-button-w-text: var(--color-black); /* not-found in uxbee.css */
    --color-button-w-border: transparent; /* not-found in uxbee.css */
    --color-button-w-bg-hover: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-w-text-hover: var(--color-white); /* not-found in uxbee.css */
    --color-button-w-border-hover: transparent; /* not-found in uxbee.css */
    --color-button-w-bg-disabled: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-w-text-disabled: var(--color-white); /* not-found in uxbee.css */
    --color-button-w-border-disabled: transparent; /* not-found in uxbee.css */

    /* gray */
    --color-button-g-bg: var(--color-gray); /* not-found in uxbee.css */
    --color-button-g-text: var(--color-gray); /* not-found in uxbee.css */
    --color-button-g-border: var(--color-gray); /* not-found in uxbee.css */
    --color-button-g-bg-hover: var(--color-gray-2); /* not-found in uxbee.css */
    --color-button-g-text-hover: var(--color-gray); /* not-found in uxbee.css */
    --color-button-g-border-hover: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-g-bg-disabled: var(--color-primary-2); /* not-found in uxbee.css */
    --color-button-g-text-disabled: var(--color-gray); /* not-found in uxbee.css */
    --color-button-g-border-disabled: var(--color-primary-2); /* not-found in uxbee.css */
    /* === button outline === */
    /* primary */
    --color-button-outline-bg: var(--color-white-1);
    --color-button-outline-text: var(--color-primary-1);
    --color-button-outline-border: var(--color-primary-1);
    --color-button-outline-text-hover: var(--color-white-1);
    --color-button-outline-border-hover: var(--color-primary-1);
    --color-button-outline-text-disabled: var(--color-white-1);
    --color-button-outline-border-disabled: #ECB7C6;
    --color-button-outline-bg-hover: var(--color-primary-1);
    --color-button-outline-bg-disabled: #ECB7C6;
    /* secondary */
    --color-button-outline-s-bg: var(--color-white-1);
    --color-button-outline-s-text: var(--color-secondary-1);
    --color-button-outline-s-border: var(--color-secondary-1);
    --color-button-outline-s-text-hover: var(--color-white-1);
    --color-button-outline-s-border-hover: var(--color-secondary-1);
    --color-button-outline-s-text-disabled: var(--color-white-1);
    --color-button-outline-s-border-disabled: #B1DCD9;
    --color-button-outline-s-bg-hover: var(--color-secondary-1);
    --color-button-outline-s-bg-disabled: #B1DCD9;

    /* tertiary */
    --color-button-outline-t-bg: var(--color-tertiary-1);
    --color-button-outline-t-text: var(--color-tertiary-1);
    --color-button-outline-t-border: var(--color-tertiary-1);
    --color-button-outline-t-text-hover: var(--color-white-1);
    --color-button-outline-t-border-hover: var(--color-tertiary-1);
    --color-button-outline-t-text-disabled: var(--color-white-1);
    --color-button-outline-t-border-disabled: #C0C4F1;
    --color-button-outline-t-bg-hover: var(--color-tertiary-1);
    --color-button-outline-t-bg-disabled: #C0C4F1;

    /* quaternary */
    --color-button-outline-q-bg: var(--color-white-1);
    --color-button-outline-q-text: var(--color-quaternary-1);
    --color-button-outline-q-border: var(--color-quaternary-1);
    --color-button-outline-q-text-hover: var(--color-white-1);
    --color-button-outline-q-border-hover: var(--color-quaternary-1);
    --color-button-outline-q-text-disabled: var(--color-white-1);
    --color-button-outline-q-border-disabled: #ADCEE3;
    --color-button-outline-q-bg-hover: var(--color-quaternary-1);
    --color-button-outline-q-bg-disabled: #ADCEE3;

    /* Navigation */
    --color-nav-main-bg: var(--color-secondary-1);
    --color-nav-main-text: var(--color-white-1);
    --color-nav-main-border: transparent; /* not-found in uxbee.css */
    --color-nav-main-bg-hover: transparent; /* not-found in uxbee.css */
    --color-nav-main-text-hover: var(--color-primary-1);
    --color-nav-main-border-hover: transparent; /* not-found in uxbee.css */
    --color-nav-main-bg-disabled: var(--color-white); /* not-found in uxbee.css */
    --color-nav-main-text-disabled: #8c8c8c; /* not-found in uxbee.css */
    --color-nav-main-border-disabled: transparent; /* not-found in uxbee.css */
    --color-nav-main-bg-selected: var(--color-white); /* not-found in uxbee.css */
    --color-nav-main-text-selected: var(--color-primary-1); /* not-found in uxbee.css */
    --color-nav-main-border-selected: transparent; /* not-found in uxbee.css */

    --color-nav-sub-bg: var(--color-white); /* not-found in uxbee.css */
    --color-nav-sub-text: var(--color-black); /* not-found in uxbee.css */
    --color-nav-sub-border: transparent; /* not-found in uxbee.css */
    --color-nav-sub-bg-hover: var(--color-white); /* not-found in uxbee.css */
    --color-nav-sub-text-hover: var(--color-primary-1); /* not-found in uxbee.css */
    --color-nav-sub-border-hover: transparent; /* not-found in uxbee.css */
    --color-nav-sub-bg-disabled: var(--color-white); /* not-found in uxbee.css */
    --color-nav-sub-text-disabled: #8c8c8c; /* not-found in uxbee.css */
    --color-nav-sub-border-disabled: transparent; /* not-found in uxbee.css */
    --color-nav-sub-bg-selected: var(--color-white); /* not-found in uxbee.css */
    --color-nav-sub-text-selected: var(--color-primary-1); /* not-found in uxbee.css */
    --color-nav-sub-border-selected: transparent; /* not-found in uxbee.css */


    /*button rounded */
    --color-button-rounded-bg: var(--color-primary-1); /* not-found in uxbee.css */
    --color-button-rounded-text: var(--color-black); /* not-found in uxbee.css */
    --color-button-rounded-border: #e6e6e6; /* not-found in uxbee.css */
    --color-button-rounded-bg-hover: var(--color-primary-1); /* not-found in uxbee.css */
    --color-button-rounded-text-hover: var(--color-white); /* not-found in uxbee.css */
    --color-button-rounded-border-hover: transparent; /* not-found in uxbee.css */
    --color-button-rounded-bg-disabled: #da9ba2; /* not-found in uxbee.css */
    --color-button-rounded-text-disabled: var(--color-white); /* not-found in uxbee.css */
    --color-button-rounded-border-disabled: transparent; /* not-found in uxbee.css */

    /* Progress Bar Colors */
    --color-progressbar-bg-1: #F57D37; /* not-found in uxbee.css */
    --color-progressbar-bg-2: #003C5F; /* not-found in uxbee.css */
    --color-progressbar-bg-3: #00C8F5; /* not-found in uxbee.css */
    --color-progressbar-bg-4: #08AE50; /* not-found in uxbee.css */
    --color-progressbar-bg-5: var(--color-primary-1); /* not-found in uxbee.css */
    --color-progressbar-bg-6: #E8E8E8; /* not-found in uxbee.css */
    --color-progressbar-bg-7: #D7D7D7; /* not-found in uxbee.css */

    /* Progress Bar Pressed Colors */
    --color-progressbarpressed-bg-1: #C4801C; /* not-found in uxbee.css */
    --color-progressbarpressed-bg-2: #304E6C; /* not-found in uxbee.css */
    --color-progressbarpressed-bg-3: #4C99B4; /* not-found in uxbee.css */
    --color-progressbarpressed-bg-4: #4F9A62; /* not-found in uxbee.css */
    --color-progressbarpressed-bg-5: #A9333A; /* not-found in uxbee.css */

    /* Text Pressed Colors */
    --color-textpressed-bg-1: #525252; /* not-found in uxbee.css */
    --color-textdeletepressed-bg-1: #878787; /* not-found in uxbee.css */
    --color-labeledpressed-bg-1: #e6e6e6; /* not-found in uxbee.css */

    /* black */
    --color-black-bg-1: #000000;
    --color-black-1: #000000;

    /* Additional colors from output.css */
    --color-quaternary-1: #D62839;
    --color-quinary: #008DD5;
    --color-senary: #FFE395;
    --color-septiary: #EBF8FF;
    --color-white-1: #FFFFFF;
    --color-gray-1: #F2F2F2;
    --color-page-background: var(--color-gray-1);
    --color-block: var(--color-senary);
    --color-block-2: var(--color-septenary);
    --color-block-3: var(--color-white-1);
    --color-block-4: var(--color-tertiary-1);
    --color-colored-section: var(--color-primary-1);
    --color-colored-section-2: var(--color-secondary-1);
    --color-component-h3: var(--color-title-3);
    --color-component-h3-hover: var(--color-primary-1);
    --color-success: #00FF15;
    --color-success--hover: var(--btn-success-solid-text-color);

    /* Missing variants - created from -1 values in output.css */
    --color-secondary-2: var(--color-secondary-1);
    --color-secondary-3: var(--color-secondary-1);
    --color-secondary-4: var(--color-secondary-1);
    --color-tertiary-2: var(--color-tertiary-1);
    --color-tertiary-3: var(--color-tertiary-1);
    --color-tertiary-4: var(--color-tertiary-1);
    --color-quaternary-2: var(--color-quaternary-1);
    --color-quaternary-3: var(--color-quaternary-1);
    --color-quaternary-4: var(--color-quaternary-1);
    --color-title-2: var(--color-tertiary-1);
    --color-title-3: var(--color-black-1);
    --color-title-4: var(--color-black-1);
    --color-title-5: var(--color-black-1);
    --color-title-6: var(--color-black-1);
    --color-body-2: var(--color-body-1);
    --color-body-3: var(--color-body-1);
    --color-body-4: var(--color-body-1);
    --color-gray-2: var(--color-gray-1);
    --color-gray-3: var(--color-gray-1);
    --color-gray-4: var(--color-gray-1);
    --color-white-2: var(--color-white-1);
    --color-white-3: var(--color-white-1);
    --color-white-4: var(--color-white-1);
    --color-black-2: var(--color-black-1);
    --color-black-3: var(--color-black-1);
    --color-black-4: var(--color-black-1);

    /* FONT FAMILY */
    --font-family-body: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading-bold: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading-h1: var(--font-family-heading);
    --font-family-heading-non-h1: var(--font-family-heading);
    --font-family-heading-h2: var(--font-family-heading-non-h1);
    --font-family-heading-h3: var(--font-family-heading-non-h1);
    --font-family-heading-h4: var(--font-family-heading-non-h1);
    --font-family-heading-h5: var(--font-family-heading-non-h1);
    --font-family-heading-h6: var(--font-family-heading-non-h1);
    --font-body-big: 1.25rem;
    --font-body-normal: 1.125rem;
    --font-body-small: 1rem;
    --font-body-xsmall: 0.875rem;
    --font-h1-value: 2.5rem;
    /* 40px */
    --font-h2-value: 2rem;
    /* 32px */
    --font-h3-value: 1.5rem;
    /* 24px */
    --font-h4-value: 1.125rem;
    /* 18px */
    --font-h5-value: 1.125rem;
    /* 18px */
    --font-h6-value: 0.875rem;
    /* 14px */
    --font-p-value: 1.125rem;
    /* 18px */
    --font-h1: var(--font-h1-value);
    --font-h2: var(--font-h2-value);
    --font-h3: var(--font-h3-value);
    --font-h4: var(--font-h4-value);
    --font-h5: var(--font-h5-value);
    --font-h6: var(--font-h6-value);
    --font-p: var(--font-p-value);
    --font-exhibitor-card-value: 1rem;
    --font-items-overview-body-value: 1.125rem;
    --font-items-overview-title-value: 1.5rem;
    --font-news-search-block-title-value: 1.5rem;
    --font-news-search-block-date-value: 1.125rem;
    --font-news-search-list-title-value: 1.25rem;
    --font-news-search-list-date-value: 1rem;
    --font-exhibitor-card: var(--font-exhibitor-card-value);
    --font-items-overview-title: var(--font-items-overview-title-value);
    --font-items-overview-body: var(--font-items-overview-body-value);
    --font-news-search-block-title: var(--font-news-search-block-title-value);
    --font-news-search-block-date: var(--font-news-search-block-date-value);
    --font-news-search-list-title: var(--font-news-search-list-title-value);
    --font-news-search-list-date: var(--font-news-search-list-date-value);
    --font-h1-line-height: 1.2;
    --font-h2-line-height: 1.2;
    --font-h3-line-height: 1.5;
    --font-h4-line-height: 1.5;
    --font-h5-line-height: 1.5;
    --font-h6-line-height: 1.5;
    --font-p-line-height: 1.6;
    --font-heading-weight: 700;

    --link-text-decoration-line: none;
    --link-text-decoration-line-hover: none;

    /* shadow */
    --card-boxshadow: 0px 12px 24px #00000029;
    --card-hover-boxshadow: 0px 4px 8px #00000029;
    --card-hover-gray: 0px 6px 20px #00000014;

    --richtext-button-line-height: var(--font-p-line-height);
}



/* FONT */
ul {
    @apply text-brand-title-1 list-inside pl-[10px];
    /* Ensures the text remains black & sets spacing */
}

ul li::marker {
    @apply text-brand-button-bg text-[32px];
    /* Makes the bullet point red and larger */
    line-height: 0;
    /* Moves the dot higher */
    vertical-align: middle;
    /* Adjusts vertical position */
}

/* FONT */


/* LINK LIST */
.text__color,
.item__spacing__column {
    text-decoration: none;
}

/* LINK LIST */

/* ICON CARD */
/* CONTENT CARD */
.padding__content {
    padding: 0px 32px 24px 32px;
}

.content-card {
    display: block;
    font-family: "Gotham A", "Gotham B", "Arial", sans-serif;
    color: inherit;
    text-decoration: none;
    background-color: var(--color-white);
    box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.16);
    transition: box-shadow 300ms ease-in-out, transform 300ms ease-in-out;
    border-radius: 8px;
}

.content-card:hover {
    cursor: pointer !important;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.16);
}

/* btn primary outline */


html {
    scroll-behavior: smooth;
}

.btn-primary-vw {
    @apply flex items-center justify-start gap-2 rounded-md bg-[#fcf2f3] text-brand-primary-1 hover:bg-brand-primary-1 hover:text-white gap-x-4
}

.btn-secondary-vw {
    @apply bg-white !text-black font-bold
}

.btn-linkedin-vw {
    @apply bg-white border border-brand-primary-1 hover:border-[#0078C7] rounded-md hover:bg-[#0078C7] text-brand-primary-1 transition-all duration-200 box-border hover:cursor-pointer hover:text-white
}