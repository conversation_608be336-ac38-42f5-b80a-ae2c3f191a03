const fs = require('fs');
const path = require('path');

// Convert Windows-style path to WSL-style path
const fixPath = (inputPath) => {
    return inputPath.replace(/\\/g, '/').replace(/^([A-Za-z]):/, (_, drive) => `/mnt/${drive.toLowerCase()}`);
};

if (process.argv.length < 4) {
    console.error('Usage: node replacecss.js new.css css-to-be-replaced-in-place.css');
    process.exit(1);
}

let [,, sourceCssPath, targetCssPath] = process.argv;

if (process.platform === 'linux') {
    sourceCssPath = fixPath(sourceCssPath);
    targetCssPath = fixPath(targetCssPath);
}

// Ensure both files exist
if (!fs.existsSync(sourceCssPath) || !fs.existsSync(targetCssPath)) {
    console.error('❌ One or both input files do not exist.');
    process.exit(1);
}

// Extract CSS variables in form: --var-name: value;
function extractVariables(css) {
    const vars = {};
    css.split('\n').forEach(line => {
        const match = line.match(/(--[\w-]+):\s*(.+?);/);
        if (match) {
            const [, name, value] = match;
            vars[name] = value.trim();
        }
    });
    return vars;
}

// Load and parse both CSS files
const sourceCss = fs.readFileSync(sourceCssPath, 'utf8');
const targetCss = fs.readFileSync(targetCssPath, 'utf8');

const sourceVars = extractVariables(sourceCss);
const targetCssLines = targetCss.split('\n');

// Track replaced vars and unmatched original lines
const updatedLines = [];
const replacedVars = new Set();
const unmatchedTargetLines = [];

targetCssLines.forEach((line) => {
    const match = line.match(/(--[\w-]+):\s*.+?;/);
    if (match) {
        const varName = match[1];
        if (sourceVars[varName]) {
            updatedLines.push(`  ${varName}: ${sourceVars[varName]}; // replaced`);
            replacedVars.add(varName);
        } else {
            unmatchedTargetLines.push(line);
            updatedLines.push(line);
        }
    } else {
        updatedLines.push(line); // Preserve other lines
    }
});

// Determine source variables that were NOT used in the target
const unusedSourceVars = Object.entries(sourceVars)
    .filter(([key]) => !replacedVars.has(key))
    .map(([key, value]) => `  ${key}: ${value};`);

const unusedCssBlock = `html {\n${unusedSourceVars.join('\n')}\n}\n`;

const unmatchedPath = path.join(path.dirname(targetCssPath), 'no_matching.txt');
fs.writeFileSync(targetCssPath, updatedLines.join('\n'), 'utf8');
fs.writeFileSync(unmatchedPath, unusedCssBlock, 'utf8');

console.log(`✅ CSS updated in-place: ${targetCssPath}`);
console.log(`📄 Unmatched variables saved to: ${unmatchedPath}`);

