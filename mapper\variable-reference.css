@import url("./font-family-uxbee.css");

html[data-theme="uxbee-corporate"] {
    --color-primary-1: #FFBC00;
    --color-primary-2: #FFBC00;
    --color-primary-3: #FFBC00;
    --color-primary-4: #FFBC00;
    --color-secondary-1: #252B1E;
    --color-tertiary-1: #9AA899;
    --color-tertiary-2: #9AA899;
    --color-quarternary-1: #D62839;
    --color-black: #0D0D0D;
    --color-white: #FFFFFF;
    --color-gray: #F7F7F7;
    --color-gray-2: #4a4949;
    --color-light-gray: #1A1A1A;
    --color-secondary-2: #fc9851;
    --color-secondary-3: #839dae;
    --color-secondary-4: #c3cdd7;
    --color-accent1-1: #d7803f;
    --color-accent1-2: #df9f6d;
    --color-accent1-3: #e8bf9d;
    --color-accent1-4: #f3dfce;
    --color-accent2-1: #db5b45;
    --color-accent2-2: #e08372;
    --color-accent2-3: #e8aca1;
    --color-accent2-4: #f2d6d0;
    --color-accent3-1: #79c7f3;
    --color-accent3-2: #8bd5f5;
    --color-accent3-3: #a9e2f9;
    --color-accent3-4: #d1f0fb;
    --color-accent4-1: #548cbc;
    --color-accent4-2: #72a8cc;
    --color-accent4-3: #a9e2f9;
    --color-accent4-4: #d1f0fb;
    --color-accent5-1: #223c5e;
    --color-accent5-2: #536d86;
    --color-accent5-3: #8b9dae;
    --color-title-1: var(--color-black);
    --color-title-2: var(--color-title-1);
    --color-title-3: var(--color-title-1);
    --color-title-4: var(--color-title-1);
    --color-title-5: var(--color-title-1);
    --color-title-6: var(--color-title-1);
    --color-title-7: var(--color-title-1);
    --color-body-1: var(--color-black);
    --color-body-2: var(--color-body-1);
    --color-body-3: var(--color-body-1);
    --color-body-4: var(--color-body-1);
    --color-dividers-1: #b9b9b9;
    --color-dividers-2: #ececec;
    --color-dividers-3: #f3f3f3;
    --color-dividers-4: #f9f9f9;
    --color-strokes-1: #e6e6e6;
    --color-strokes-2: #606060;
    --color-strokes-3: #f3f3f3;
    --color-strokes-4: #f9f9f9;
    --color-common-bg-1: #f8f8f8;
    --color-widget-bg-1: var(--color-white);
    --color-link-text: var(--color-primary-1);
    --color-footer-1: #1a1a1a;
    --color-footer-2: #B1B1B1;
    --color-alt-secondary-hover: #EBEBEB;

    /* skin */
    /* Style */
    --btn-corners: 0.375rem;
    /* 6px */

    /* === button solid ===*/

    /* primary */
    --color-button-bg: var(--color-primary-1);
    --color-button-text: var(--color-white);
    --color-button-border: transparent;
    --color-button-bg-hover: var(--color-primary-2);
    --color-button-text-hover: var(--color-white);
    --color-button-border-hover: transparent;
    --color-button-bg-disabled: var(--color-primary-2);
    --color-button-text-disabled: var(--color-white);
    --color-button-border-disabled: transparent;

    /* secondary */
    --color-button-s-bg: var(--color-secondary-1);
    --color-button-s-text: var(--color-white);
    --color-button-s-border: transparent;
    --color-button-s-bg-hover: var(--color-secondary-2);
    --color-button-s-text-hover: var(--color-white);
    --color-button-s-border-hover: transparent;
    --color-button-s-bg-disabled: var(--color-secondary-2);
    --color-button-s-text-disabled: var(--color-white);
    --color-button-s-border-disabled: transparent;

    /* tertiary */
    --color-button-t-bg: var(--color-tertiary-1);
    --color-button-t-text: var(--color-white);
    --color-button-t-border: transparent;
    --color-button-t-bg-hover: var(--color-tertiary-2);
    --color-button-t-text-hover: var(--color-white);
    --color-button-t-border-hover: transparent;
    --color-button-t-bg-disabled: var(--color-tertiary-2);
    --color-button-t-text-disabled: var(--color-white);
    --color-button-t-border-disabled: transparent;

    /* quarternary */
    --color-button-q-bg: var(--color-quarternary-1);
    --color-button-q-text: var(--color-white);
    --color-button-q-border: transparent;
    --color-button-q-bg-hover: var(--color-tertiary-1);
    --color-button-q-text-hover: var(--color-white);
    --color-button-q-border-hover: transparent;
    --color-button-q-bg-disabled: var(--color-tertiary-1);
    --color-button-q-text-disabled: var(--color-white);
    --color-button-q-border-disabled: transparent;

    /* black */
    --color-button-b-bg: var(--color-black);
    --color-button-b-text: var(--color-white);
    --color-button-b-border: transparent;
    --color-button-b-bg-hover: var(--color-primary-2);
    --color-button-b-text-hover: var(--color-white);
    --color-button-b-border-hover: transparent;
    --color-button-b-bg-disabled: var(--color-primary-2);
    --color-button-b-text-disabled: var(--color-white);
    --color-button-b-border-disabled: transparent;

    /* white */
    --color-button-w-bg: var(--color-white);
    --color-button-w-text: var(--color-black);
    --color-button-w-border: transparent;
    --color-button-w-bg-hover: var(--color-primary-2);
    --color-button-w-text-hover: var(--color-white);
    --color-button-w-border-hover: transparent;
    --color-button-w-bg-disabled: var(--color-primary-2);
    --color-button-w-text-disabled: var(--color-white);
    --color-button-w-border-disabled: transparent;

    /* gray */
    --color-button-g-bg: var(--color-gray);
    --color-button-g-text: var(--color-gray);
    --color-button-g-border: var(--color-gray);
    --color-button-g-bg-hover: var(--color-gray-2);
    --color-button-g-text-hover: var(--color-gray);
    --color-button-g-border-hover: var(--color-primary-2);
    --color-button-g-bg-disabled: var(--color-primary-2);
    --color-button-g-text-disabled: var(--color-gray);
    --color-button-g-border-disabled: var(--color-primary-2);
    /* === button outline === */
    /* primary */
    --color-button-outline-text: var(--color-primary-1);
    --color-button-outline-border: var(--color-primary-1);
    --color-button-outline-text-hover: var(--color-primary-2);
    --color-button-outline-border-hover: var(--color-primary-2);
    --color-button-outline-text-disabled: var(--color-primary-2);
    --color-button-outline-border-disabled: var(--color-primary-2);
    --color-button-outline-bg-hover: var(--color-white);
    /* secondary */
    --color-button-outline-s-text: var(--color-secondary-1);
    --color-button-outline-s-border: var(--color-secondary-1);
    --color-button-outline-s-text-hover: var(--color-secondary-2);
    --color-button-outline-s-border-hover: var(--color-secondary-2);
    --color-button-outline-s-text-disabled: var(--color-secondary-2);
    --color-button-outline-s-border-disabled: var(--color-secondary-2);
    --color-button-outline-s-bg-hover: var(--color-white);

    /* tertiary */
    --color-button-outline-t-text: var(--color-tertiary-1);
    --color-button-outline-t-border: var(--color-tertiary-1);
    --color-button-outline-t-text-hover: var(--color-tertiary-2);
    --color-button-outline-t-border-hover: var(--color-tertiary-2);
    --color-button-outline-t-text-disabled: var(--color-tertiary-2);
    --color-button-outline-t-border-disabled: var(--color-tertiary-2);
    --color-button-outline-t-bg-hover: var(--color-white);

    /* quaternary */
    --color-button-outline-q-text: var(--color-quarternary-1);
    --color-button-outline-q-border: var(--color-quarternary-1);
    --color-button-outline-q-text-hover: var(--color-tertiary-1);
    --color-button-outline-q-border-hover: var(--color-tertiary-1);
    --color-button-outline-q-text-disabled: var(--color-tertiary-1);
    --color-button-outline-q-border-disabled: var(--color-tertiary-1);
    --color-button-outline-q-bg-hover: var(--color-white);

    /* Navigation */
    --color-nav-main-bg: var(--color-white);
    --color-nav-main-text: var(--color-black);
    --color-nav-main-border: transparent;
    --color-nav-main-bg-hover: transparent;
    --color-nav-main-text-hover: var(--color-primary-1);
    --color-nav-main-border-hover: transparent;
    --color-nav-main-bg-disabled: var(--color-white);
    --color-nav-main-text-disabled: #8c8c8c;
    --color-nav-main-border-disabled: transparent;
    --color-nav-main-bg-selected: var(--color-white);
    --color-nav-main-text-selected: var(--color-primary-1);
    --color-nav-main-border-selected: transparent;

    --color-nav-sub-bg: var(--color-white);
    --color-nav-sub-text: var(--color-black);
    --color-nav-sub-border: transparent;
    --color-nav-sub-bg-hover: var(--color-white);
    --color-nav-sub-text-hover: var(--color-primary-1);
    --color-nav-sub-border-hover: transparent;
    --color-nav-sub-bg-disabled: var(--color-white);
    --color-nav-sub-text-disabled: #8c8c8c;
    --color-nav-sub-border-disabled: transparent;
    --color-nav-sub-bg-selected: var(--color-white);
    --color-nav-sub-text-selected: var(--color-primary-1);
    --color-nav-sub-border-selected: transparent;


    /*button rounded */
    --color-button-rounded-bg: var(--color-primary-1);
    --color-button-rounded-text: var(--color-black);
    --color-button-rounded-border: #e6e6e6;
    --color-button-rounded-bg-hover: var(--color-primary-1);
    --color-button-rounded-text-hover: var(--color-white);
    --color-button-rounded-border-hover: transparent;
    --color-button-rounded-bg-disabled: #da9ba2;
    --color-button-rounded-text-disabled: var(--color-white);
    --color-button-rounded-border-disabled: transparent;

    /*progress bar */
    --color-progressbar-bg-1: #F57D37;
    --color-progressbar-bg-2: #003C5F;
    --color-progressbar-bg-3: #00C8F5;
    --color-progressbar-bg-4: #08AE50;
    --color-progressbar-bg-5: var(--color-primary-1);
    --color-progressbar-bg-6: #E8E8E8;
    --color-progressbar-bg-7: #D7D7D7;

    /*  text on pressed*/
    --color-textpressed-bg-1: #525252;
    --color-textdeletepressed-bg-1: #878787;
    --color-labeledpressed-bg-1: #e6e6e6;
    --color-progressbarpressed-bg-1: #C4801C;
    --color-progressbarpressed-bg-2: #304E6C;
    --color-progressbarpressed-bg-3: #4C99B4;
    --color-progressbarpressed-bg-4: #4F9A62;
    --color-progressbarpressed-bg-5: #A9333A;

    /* black */
    --color-black-bg-1: #000000;

    /* FONT FAMILY */
    --font-family-body: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading-bold: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading: "Gotham A", "Gotham B", "Verdana";
    --font-family-heading-h1: var(--font-family-heading);
    --font-family-heading-non-h1: var(--font-family-heading);
    --font-family-heading-h2: var(--font-family-heading-non-h1);
    --font-family-heading-h3: var(--font-family-heading-non-h1);
    --font-family-heading-h4: var(--font-family-heading-non-h1);
    --font-family-heading-h5: var(--font-family-heading-non-h1);
    --font-family-heading-h6: var(--font-family-heading-non-h1);
    --font-body-big: 1.25rem;
    --font-body-normal: 1.125rem;
    --font-body-small: 1rem;
    --font-body-xsmall: 0.875rem;
    --font-h1-value: 2.5rem;
    /* 40px */
    --font-h2-value: 2rem;
    /* 32px */
    --font-h3-value: 1.5rem;
    /* 24px */
    --font-h4-value: 1.125rem;
    /* 18px */
    --font-h5-value: 1.125rem;
    /* 18px */
    --font-h6-value: 0.875rem;
    /* 14px */
    --font-p-value: 1.125rem;
    /* 18px */
    --font-h1: var(--font-h1-value);
    --font-h2: var(--font-h2-value);
    --font-h3: var(--font-h3-value);
    --font-h4: var(--font-h4-value);
    --font-h5: var(--font-h5-value);
    --font-h6: var(--font-h6-value);
    --font-p: var(--font-p-value);
    --font-exhibitor-card-value: 1rem;
    --font-items-overview-body-value: 1.125rem;
    --font-items-overview-title-value: 1.5rem;
    --font-news-search-block-title-value: 1.5rem;
    --font-news-search-block-date-value: 1.125rem;
    --font-news-search-list-title-value: 1.25rem;
    --font-news-search-list-date-value: 1rem;
    --font-exhibitor-card: var(--font-exhibitor-card-value);
    --font-items-overview-title: var(--font-items-overview-title-value);
    --font-items-overview-body: var(--font-items-overview-body-value);
    --font-news-search-block-title: var(--font-news-search-block-title-value);
    --font-news-search-block-date: var(--font-news-search-block-date-value);
    --font-news-search-list-title: var(--font-news-search-list-title-value);
    --font-news-search-list-date: var(--font-news-search-list-date-value);
    --font-h1-line-height: 1.2;
    --font-h2-line-height: 1.2;
    --font-h3-line-height: 1.5;
    --font-h4-line-height: 1.5;
    --font-h5-line-height: 1.5;
    --font-h6-line-height: 1.5;
    --font-p-line-height: 1.6;
    --font-heading-weight: 700;

    --link-text-decoration-line: none;
    --link-text-decoration-line-hover: none;

    /* shadow */
    --card-boxshadow: 0px 12px 24px #00000029;
    --card-hover-boxshadow: 0px 4px 8px #00000029;
    --card-hover-gray: 0px 6px 20px #00000014;

    --richtext-button-line-height: var(--font-p-line-height);
}



/* FONT */
ul {
    @apply text-brand-title-1 list-inside pl-[10px];
    /* Ensures the text remains black & sets spacing */
}

ul li::marker {
    @apply text-brand-button-bg text-[32px];
    /* Makes the bullet point red and larger */
    line-height: 0;
    /* Moves the dot higher */
    vertical-align: middle;
    /* Adjusts vertical position */
}

/* FONT */


/* LINK LIST */
.text__color,
.item__spacing__column {
    text-decoration: none;
}

/* LINK LIST */

/* ICON CARD */
/* CONTENT CARD */
.padding__content {
    padding: 0px 32px 24px 32px;
}

.content-card {
    display: block;
    font-family: "Gotham A", "Gotham B", "Arial", sans-serif;
    color: inherit;
    text-decoration: none;
    background-color: var(--color-white);
    box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.16);
    transition: box-shadow 300ms ease-in-out, transform 300ms ease-in-out;
    border-radius: 8px;
}

.content-card:hover {
    cursor: pointer !important;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.16);
}

/* btn primary outline */


html {
    scroll-behavior: smooth;
}

.btn-primary-vw {
    @apply flex items-center justify-start gap-2 rounded-md bg-[#fcf2f3] text-brand-primary-1 hover:bg-brand-primary-1 hover:text-white gap-x-4
}

.btn-secondary-vw {
    @apply bg-white !text-black font-bold
}

.btn-linkedin-vw {
    @apply bg-white border border-brand-primary-1 hover:border-[#0078C7] rounded-md hover:bg-[#0078C7] text-brand-primary-1 transition-all duration-200 box-border hover:cursor-pointer hover:text-white
}