html {
    /* ==== Fonts ======= */
    --font-family-heading: "AlrightSansBold";
    --font-family-heading-h1: var(--font-family-heading);
    --font-family-heading-non-h1: var(--font-family-heading);
    --font-family-heading-h2: var(--font-family-heading-non-h1);
    --font-family-heading-h3: var(--font-family-heading-non-h1);
    --font-family-heading-h4: var(--font-family-heading-non-h1);
    --font-family-heading-h5: var(--font-family-heading-non-h1);
    --font-family-heading-h6: var(--font-family-heading-non-h1);
    --font-family-body: "DIN Next LT Pro";
    --font-h1-value: 2.625rem;
    --font-h2-value: 2rem;
    --font-h3-value: 1.5rem;
    --font-h4-value: 1.125rem;
    --font-h5-value: 1rem;
    --font-h6-value: 1rem;
    --font-p-value: 1.125rem;
    --font-exhibitor-card-value: 1rem;
    --font-items-overview-body-value: 1.125rem;
    --font-items-overview-title-value: 1.5rem;
    --font-news-search-block-title-value: 1.5rem;
    --font-news-search-block-date-value: 1.125rem;
    --font-news-search-list-title-value: 1.25rem;
    --font-news-search-list-date-value: 1rem;
    --font-news-detail-tag: 1rem;
    --font-h1: var(--font-h1-value);
    --font-h2: var(--font-h2-value);
    --font-h3: var(--font-h3-value);
    --font-h4: var(--font-h4-value);
    --font-h5: var(--font-h5-value);
    --font-h6: var(--font-h6-value);
    --font-p: var(--font-p-value);
    --font-exhibitor-card: var(--font-exhibitor-card-value);
    --font-items-overview-title: var(--font-items-overview-title-value);
    --font-items-overview-body: var(--font-items-overview-body-value);
    --font-news-search-block-title: var(--font-news-search-block-title-value);
    --font-news-search-block-date: var(--font-news-search-block-date-value);
    --font-news-search-list-title: var(--font-news-search-list-title-value);
    --font-news-search-list-date: var(--font-news-search-list-date-value);
    --font-h1-line-height: 1.2;
    --font-h2-line-height: 1.2;
    --font-h3-line-height: 1.5;
    --font-h4-line-height: 1.5;
    --font-h5-line-height: 1.5;
    --font-h6-line-height: 1.5;
    --font-p-line-height: 1.5;
    --font-heading-weight: 700;

    /* ====== Main Colors ========= */
    --color-title-1: var(--color-secondary-1);
    --color-title-2: var(--color-secondary-1);
    --color-title-3: var(--color-secondary-1);
    --color-title-4: var(--color-secondary-1);
    --color-body-1: var(--color-secondary-1);
    --color-body-2: #747474;
    --color-body-3: var(--color-secondary-1);
    --color-body-4: var(--color-secondary-1);
    --color-dividers-1: #e6e6e6;
    --color-dividers-2: #ececec;
    --color-dividers-3: #f3f3f3;
    --color-dividers-4: #f9f9f9;
    --color-strokes-1: #e6e6e6;
    --color-strokes-2: #ececec;
    --color-strokes-3: #f3f3f3;
    --color-strokes-4: #f9f9f9;
    --color-common-bg-1: #f8f8f8;
    --color-filter-bg-1: var(--color-common-bg-1);
    --color-widget-bg-1: #ffffff;
    --color-base-1: #ffffff;
    --color-body-bg-primary-1: #fafafa;
    --color-body-bg-secondary-1: white;
    --color-body-bg-tertiary-1: transparent;
    --color-body-bg-quaternary-1: #fafafa;
    --color-danger: #dc3545;
    --color-danger--hover: #a71d2a;
    --color-success: #28a745;
    --color-success--hover: #19692c;

    /* ==== scrollbar width size ==== */
    --scrollbar-width: 14px;
    /* ===== components background color ===== */

    /* content-card */
    --color-content-card-bg-primary-1: transparent;

    /* ===== radius ======= */
    --rounded-primary: 1rem;
    --rounded-secondary: 0.375rem;
    --rounded-tertiary: 0.5rem;
    --rounded-quaternary: 0.75rem;

    /* ====- components radius ==== */
    /* button */
    --button-rounded-primary: 0rem;

    --specific-rounded-top-left: var(--button-rounded-primary);
    --specific-rounded-top-right: var(--button-rounded-primary);
    --specific-rounded-bottom-left: var(--button-rounded-primary);
    --specific-rounded-bottom-right: var(--button-rounded-primary);

    --rounded-novelties-filter-by-category: var(--button-rounded-primary);

    /* column-splitter */
    --column-splitter-rounded-primary: 0;
    /* content-card */
    --content-card-rounded-primary: 0;
    /* card rounded */
    --card-rounded: 0;
    /* image rounded */
    --image-rounded: 0px;
    /* news detail banner rounded */
    --news-detail-banner-rounded: 0px;
    /* section */
    --section-rounded: 0px;

    --banner-font-size: var(--font-h3);
    --banner-font-weight: normal;
    --banner-text-color: black;
    --banner-text-color-hover: var(--banner-text-color);

    /* links */
    --color-link-text: var(--color-secondary-1);
    --color-link-text-hover: var(--color-primary-1);
    --link-text-decoration-line: underline;
    --link-text-decoration-line-hover: underline;
    --link-text-font-weight: normal;
    --font-link-font-family: unset;

    /* blockquote */
    --color-blockquote: var(--color-primary-2);

    /* footer */
    --color-footer-about-bg: #333333;
    --color-footer-about-text: white;
    --color-footer-about-text-hover: var(--color-footer-about-text);
    --color-footer-copyright-bg: #222222;
    --color-footer-copyright-text: var(--color-footer-about-text);
    --color-footer-copyright-text-hover: var(--color-footer-about-text-hover);
    --color-footer-copyright-social: white;
    --color-footer-button-bg: var(--color-button-bg);
    --color-footer-button-text: var(--color-button-text);
    --color-footer-button-border: var(--color-button-border);
    --color-footer-button-bg-hover: var(--color-button-bg-hover);
    --color-footer-button-text-hover: var(--color-button-text-hover);
    --color-footer-button-border-hover: var(--color-button-border-hover);
    --font-button-footer-font-family: var(--font-button-font-family);

    /* service menu */
    --color-service-menu: #7e808c;
    --color-service-menu-hover: var(--color-primary-1);
    --font-weight-service-menu: normal;
    --font-service-menu: var(--font-h6-value);

    /* main navigation */
    --font-family-nav-main-heading: var(--font-family-heading);
    --color-nav-main-bg: white;
    --color-nav-main-text: var(--color-body-1);
    --color-nav-main-text-hover: var(--color-primary-1);
    --color-nav-main-dropdown-chevron: var(--color-nav-main-text);
    --color-nav-main-dropdown-chevron-hover: var(--color-nav-main-text-hover);
    --color-nav-main-sub-nav-icon: var(--color-nav-main-text);
    --color-nav-main-sub-nav-text: var(--color-nav-main-text);
    --font-nav-main-sub-nav-font-weight: normal;
    --font-nav-main-sub-nav-font-size: inherit;
    --color-nav-main-sub-nav-icon-hover: var(--color-nav-main-text-hover);
    --color-nav-main-sub-nav-text-hover: var(--color-nav-main-text-hover);
    --font-nav-main-sub-nav-font-weight-hover: normal;
    --color-nav-main-card-link: var(--color-nav-main-text);
    --color-nav-main-card-link-hover: var(--color-nav-main-text-hover);
    --font-nav-main-button-text-weight: unset;
    --font-nav-main-button-line-height: 1.0;
    --border-nav-main-button-border-width: 2px;
    --color-nav-main-button-bg: var(--color-button-bg);
    --color-nav-main-button-text: var(--color-button-text);
    --color-nav-main-button-border: var(--color-button-border);
    --color-nav-main-button-bg-hover: var(--color-button-bg-hover);
    --color-nav-main-button-text-hover: var(--color-button-text-hover);
    --color-nav-main-button-border-hover: var(--color-button-border-hover);
    --font-family-nav-main-link: var(--font-link-font-family);

    --button-padding-x: 20px;
    --button-padding-y: 8px;

    /* === button solid ===*/
    --richtext-button-line-height: calc(var(--font-p-line-height) * 2);

    /* primary */
    --color-button-bg: var(--color-primary-1);
    --color-button-text: var(--color-base-1);
    --color-button-border: transparent;
    --color-button-bg-hover: white;
    --color-button-text-hover: var(--color-primary-1);
    --color-button-border-hover: var(--color-primary-1);
    --color-button-bg-disabled: var(--color-primary-1);
    --color-button-text-disabled: var(--color-base-1);
    --color-button-border-disabled: transparent;
    --font-button-font-weight: unset;
    --font-button-font-size: var(--font-p);
    --font-button-font-family: unset;

    /* secondary */
    --color-button-s-bg: var(--color-secondary-1);
    --color-button-s-text: var(--color-base-1);
    --color-button-s-border: transparent;
    --color-button-s-bg-hover: white;
    --color-button-s-text-hover: var(--color-secondary-1);
    --color-button-s-border-hover: var(--color-secondary-1);
    --color-button-s-bg-disabled: var(--color-secondary-1);
    --color-button-s-text-disabled: var(--color-base-1);
    --color-button-s-border-disabled: transparent;
    --font-button-s-font-weight: unset;
    --font-button-s-font-size: var(--font-p);
    --font-button-s-font-family: var(--font-button-font-family);
    /* tertiary */
    --color-button-t-bg: var(--color-tertiary-1);
    --color-button-t-text: var(--color-base-1);
    --color-button-t-border: transparent;
    --color-button-t-bg-hover: white;
    --color-button-t-text-hover: var(--color-tertiary-1);
    --color-button-t-border-hover: var(--color-tertiary-1);
    --color-button-t-bg-disabled: var(--color-tertiary-1);
    --color-button-t-text-disabled: var(--color-base-1);
    --color-button-t-border-disabled: transparent;
    --font-button-t-font-weight: unset;
    --font-button-t-font-size: var(--font-p);
    --font-button-t-font-family: var(--font-button-font-family);
    /* quaternary */
    --color-button-q-bg: var(--color-quaternary-1);
    --color-button-q-text: var(--color-base-1);
    --color-button-q-border: transparent;
    --color-button-q-bg-hover: white;
    --color-button-q-text-hover: var(--color-quaternary-1);
    --color-button-q-border-hover: var(--color-quaternary-1);
    --color-button-q-bg-disabled: var(--color-quaternary-1);
    --color-button-q-text-disabled: var(--color-base-1);
    --color-button-q-border-disabled: transparent;
    --font-button-q-font-weight: unset;
    --font-button-q-font-size: var(--font-p);
    --font-button-q-font-family: var(--font-button-font-family);
    /* white */
    --color-button-w-bg: white;
    --color-button-w-text: var(--color-primary-1);
    --color-button-w-border: transparent;
    --color-button-w-bg-hover: transparent;
    --color-button-w-text-hover: white;
    --color-button-w-border-hover: white;
    --color-button-w-bg-disabled: var(--color-primary-1);
    --color-button-w-text-disabled: var(--color-base-1);
    --color-button-w-border-disabled: transparent;
    --font-button-w-font-weight: unset;
    --font-button-w-font-size: var(--font-p);
    --font-button-w-font-family: var(--font-button-font-family);

    /* === button outline === */
    --border-button-border-width: 2px;
    /* primary */
    --color-button-outline-text: var(--color-primary-1);
    --color-button-outline-border: var(--color-primary-1);
    --color-button-outline-text-hover: white;
    --color-button-outline-bg-hover: var(--color-primary-1);
    --color-button-outline-border-hover: var(--color-primary-1);
    --color-button-outline-text-disabled: transparent;
    --color-button-outline-border-disabled: transparent;
    --font-button-outline-font-weight: unset;
    --font-button-outline-font-size: var(--font-p);
    --font-button-outline-font-family: var(--font-button-font-family);

    /* secondary */
    --color-button-outline-s-text: var(--color-secondary-1);
    --color-button-outline-s-border: var(--color-secondary-1);
    --color-button-outline-s-text-hover: white;
    --color-button-outline-s-bg-hover: var(--color-secondary-1);
    --color-button-outline-s-border-hover: var(--color-secondary-1);
    --color-button-outline-s-text-disabled: transparent;
    --color-button-outline-s-border-disabled: transparent;
    --font-button-outline-s-font-weight: unset;
    --font-button-outline-s-font-size: var(--font-p);
    --font-button-outline-s-font-family: var(--font-button-font-family);

    /* tertiary */
    --color-button-outline-t-text: var(--color-tertiary-1);
    --color-button-outline-t-border: var(--color-tertiary-1);
    --color-button-outline-t-text-hover: white;
    --color-button-outline-t-bg-hover: var(--color-tertiary-1);
    --color-button-outline-t-border-hover: var(--color-tertiary-1);
    --color-button-outline-t-text-disabled: transparent;
    --color-button-outline-t-border-disabled: transparent;
    --font-button-outline-t-font-weight: unset;
    --font-button-outline-t-font-size: var(--font-p);
    --font-button-outline-t-font-family: var(--font-button-font-family);

    /* quaternary */
    --color-button-outline-q-text: var(--color-quaternary-1);
    --color-button-outline-q-border: var(--color-quaternary-1);
    --color-button-outline-q-text-hover: white;
    --color-button-outline-q-bg-hover: var(--color-quaternary-1);
    --color-button-outline-q-border-hover: var(--color-quaternary-1);
    --color-button-outline-q-text-disabled: transparent;
    --color-button-outline-q-border-disabled: transparent;
    --font-button-outline-q-font-weight: unset;
    --font-button-outline-q-font-size: var(--font-p);
    --font-button-outline-q-font-family: var(--font-button-font-family);

    /* white */
    --color-button-outline-w-text: white;
    --color-button-outline-w-border: white;
    --color-button-outline-w-text-hover: #343a40;
    --color-button-outline-w-bg-hover: white;
    --color-button-outline-w-border-hover: white;
    --color-button-outline-w-text-disabled: transparent;
    --color-button-outline-w-border-disabled: transparent;
    --font-button-outline-w-font-weight: unset;
    --font-button-outline-w-font-size: var(--font-p);
    --font-button-outline-w-font-family: var(--font-button-font-family);


    /* === button solid on background primary ===*/
    /* primary */
    --color-button-bg-on-bg-primary: var(--color-button-bg);
    --color-button-text-on-bg-primary: var(--color-button-text);
    --color-button-border-on-bg-primary: var(--color-button-border);
    --color-button-bg-hover-on-bg-primary: var(--color-button-bg-hover);
    --color-button-text-hover-on-bg-primary: var(--color-button-text-hover);
    --color-button-border-hover-on-bg-primary: var(--color-button-border-hover);
    --color-button-text-disabled-on-bg-primary: var(--color-button-text-disabled);
    --color-button-border-disabled-on-bg-primary: var(--color-button-border-disabled);
    --font-button-font-weight-on-bg-primary: var(--font-button-font-weight);
    --font-button-font-size-on-bg-primary: var(--font-p);
    --font-button-font-family-on-bg-primary: var(--font-button-font-family);


    /* secondary */
    --color-button-s-bg-on-bg-primary: var(--color-button-s-bg);
    --color-button-s-text-on-bg-primary: var(--color-button-s-text);
    --color-button-s-border-on-bg-primary: var(--color-button-s-border);
    --color-button-s-bg-hover-on-bg-primary: var(--color-button-s-bg-hover);
    --color-button-s-text-hover-on-bg-primary: var(--color-button-s-text-hover);
    --color-button-s-border-hover-on-bg-primary: var(--color-button-s-border-hover);
    --color-button-s-bg-disabled-on-bg-primary: var(--color-button-s-bg-disabled);
    --color-button-s-text-disabled-on-bg-primary: var(--color-button-s-text-disabled);
    --color-button-s-border-disabled-on-bg-primary: var(--color-button-s-border-disabled);
    --font-button-s-font-weight-on-bg-primary: var(--font-button-s-font-weight);
    --font-button-s-font-size-on-bg-primary: var(--font-p);
    --font-button-s-font-family-on-bg-primary: var(--font-button-font-family);


    /* tertiary */
    --color-button-t-bg-on-bg-primary: var(--color-button-t-bg);
    --color-button-t-text-on-bg-primary: var(--color-button-t-text);
    --color-button-t-border-on-bg-primary: var(--color-button-t-border);
    --color-button-t-bg-hover-on-bg-primary: var(--color-button-t-bg-hover);
    --color-button-t-text-hover-on-bg-primary: var(--color-button-t-text-hover);
    --color-button-t-border-hover-on-bg-primary: var(--color-button-t-border-hover);
    --color-button-t-bg-disabled-on-bg-primary: var(--color-button-t-bg-disabled);
    --color-button-t-text-disabled-on-bg-primary: var(--color-button-t-text-disabled);
    --color-button-t-border-disabled-on-bg-primary: var(--color-button-t-border-disabled);
    --font-button-t-font-weight-on-bg-primary: var(--font-button-t-font-weight);
    --font-button-t-font-size-on-bg-primary: var(--font-p);
    --font-button-t-font-family-on-bg-primary: var(--font-button-font-family);

    /* quaternary */
    --color-button-q-bg-on-bg-primary: var(--color-button-q-bg);
    --color-button-q-text-on-bg-primary: var(--color-button-q-text);
    --color-button-q-border-on-bg-primary: var(--color-button-q-border);
    --color-button-q-bg-hover-on-bg-primary: var(--color-button-q-bg-hover);
    --color-button-q-text-hover-on-bg-primary: var(--color-button-q-text-hover);
    --color-button-q-border-hover-on-bg-primary: var(--color-button-q-border-hover);
    --color-button-q-bg-disabled-on-bg-primary: var(--color-button-q-bg-disabled);
    --color-button-q-text-disabled-on-bg-primary: var(--color-button-q-text-disabled);
    --color-button-q-border-disabled-on-bg-primary: var(--color-button-q-border-disabled);
    --font-button-q-font-weight-on-bg-primary: var(--font-button-q-font-weight);
    --font-button-q-font-size-on-bg-primary: var(--font-p);
    --font-button-q-font-family-on-bg-primary: var(--font-button-font-family);

    /* white */
    --color-button-w-bg-on-bg-primary: var(--color-button-w-bg);
    --color-button-w-text-on-bg-primary: var(--color-button-w-text);
    --color-button-w-border-on-bg-primary: var(--color-button-w-border);
    --color-button-w-bg-hover-on-bg-primary: var(--color-button-w-bg-hover);
    --color-button-w-text-hover-on-bg-primary: var(--color-button-w-text-hover);
    --color-button-w-border-hover-on-bg-primary: var(--color-button-w-border-hover);
    --color-button-w-bg-disabled-on-bg-primary: var(--color-button-w-bg-disabled);
    --color-button-w-text-disabled-on-bg-primary: var(--color-button-w-text-disabled);
    --color-button-w-border-disabled-on-bg-primary: var(--color-button-w-border-disabled);
    --font-button-w-font-weight-on-bg-primary: var(--font-button-w-font-weight);
    --font-button-w-font-size-on-bg-primary: var(--font-p);
    --font-button-w-font-family-on-bg-primary: var(--font-button-font-family);

    /* === button outline on background primary === */
    --border-button-border-width-on-bg-primary: var(--border-button-border-width);
    /* primary */
    --color-button-outline-text-on-bg-primary: var(--color-button-outline-text);
    --color-button-outline-border-on-bg-primary: var(--color-button-outline-border);
    --color-button-outline-text-hover-on-bg-primary: var(--color-button-outline-text-hover);
    --color-button-outline-bg-hover-on-bg-primary: var(--color-button-outline-bg-hover);
    --color-button-outline-border-hover-on-bg-primary: var(--color-button-outline-border-hover);
    --color-button-outline-text-disabled-on-bg-primary: var(--color-button-outline-text-disabled);
    --color-button-outline-border-disabled-on-bg-primary: var(--color-button-outline-border-disabled);
    --font-button-outline-font-weight-on-bg-primary: var(--font-button-outline-font-weight);
    --font-button-outline-font-size-on-bg-primary: var(--font-p);
    --font-button-outline-font-family-on-bg-primary: var(--font-button-font-family);

    /* secondary */
    --color-button-outline-s-text-on-bg-primary: var(--color-button-outline-s-text);
    --color-button-outline-s-border-on-bg-primary: var(--color-button-outline-s-border);
    --color-button-outline-s-text-hover-on-bg-primary: var(--color-button-outline-s-text-hover);
    --color-button-outline-s-bg-hover-on-bg-primary: var(--color-button-outline-s-bg-hover);
    --color-button-outline-s-border-hover-on-bg-primary: var(--color-button-outline-s-border-hover);
    --color-button-outline-s-text-disabled-on-bg-primary: var(--color-button-outline-s-text-disabled);
    --color-button-outline-s-border-disabled-on-bg-primary: var(--color-button-outline-s-border-disabled);
    --font-button-outline-s-font-weight-on-bg-primary: var(--font-button-outline-s-font-weight);
    --font-button-outline-s-font-size-on-bg-primary: var(--font-p);
    --font-button-outline-s-font-family-on-bg-primary: var(--font-button-font-family);

    /* tertiary */
    --color-button-outline-t-text-on-bg-primary: var(--color-button-outline-t-text);
    --color-button-outline-t-border-on-bg-primary: var(--color-button-outline-t-border);
    --color-button-outline-t-text-hover-on-bg-primary: var(--color-button-outline-t-text-hover);
    --color-button-outline-t-bg-hover-on-bg-primary: var(--color-button-outline-t-bg-hover);
    --color-button-outline-t-border-hover-on-bg-primary: var(--color-button-outline-t-border-hover);
    --color-button-outline-t-text-disabled-on-bg-primary: var(--color-button-outline-t-text-disabled);
    --color-button-outline-t-border-disabled-on-bg-primary: var(--color-button-outline-t-border-disabled);
    --font-button-outline-t-font-weight-on-bg-primary: var(--font-button-outline-t-font-weight);
    --font-button-outline-t-font-size-on-bg-primary: var(--font-p);
    --font-button-outline-t-font-family-on-bg-primary: var(--font-button-font-family);

    /* quaternary */
    --color-button-outline-q-text-on-bg-primary: var(--color-button-outline-q-text);
    --color-button-outline-q-border-on-bg-primary: var(--color-button-outline-q-border);
    --color-button-outline-q-text-hover-on-bg-primary: var(--color-button-outline-q-text-hover);
    --color-button-outline-q-bg-hover-on-bg-primary: var(--color-button-outline-q-bg-hover);
    --color-button-outline-q-border-hover-on-bg-primary: var(--color-button-outline-q-border-hover);
    --color-button-outline-q-text-disabled-on-bg-primary: var(--color-button-outline-q-text-disabled);
    --color-button-outline-q-border-disabled-on-bg-primary: var(--color-button-outline-q-border-disabled);
    --font-button-outline-q-font-weight-on-bg-primary: var(--font-button-outline-q-font-weight);
    --font-button-outline-q-font-size-on-bg-primary: var(--font-p);
    --font-button-outline-q-font-family-on-bg-primary: var(--font-button-font-family);

    /* white */
    --color-button-outline-w-text-on-bg-primary: var(--color-button-outline-w-text);
    --color-button-outline-w-border-on-bg-primary: var(--color-button-outline-w-border);
    --color-button-outline-w-text-hover-on-bg-primary: var(--color-button-outline-w-text-hover);
    --color-button-outline-w-bg-hover-on-bg-primary: var(--color-button-outline-w-bg-hover);
    --color-button-outline-w-border-hover-on-bg-primary: var(--color-button-outline-w-border-hover);
    --color-button-outline-w-text-disabled-on-bg-primary: var(--color-button-outline-w-text-disabled);
    --color-button-outline-w-border-disabled-on-bg-primary: var(--color-button-outline-w-border-disabled);
    --font-button-outline-w-font-weight-on-bg-primary: var(--font-button-outline-w-font-weight);
    --font-button-outline-w-font-size-on-bg-primary: var(--font-p);
    --font-button-outline-w-font-family-on-bg-primary: var(--font-button-font-family);



    /* mobile */
    --font-size-multiplier: 0.8;
    --font-size-multiplier-heading: var(--font-size-multiplier);

    --color-page-background: white;

    --border-color-primary--hover: var(--color-primary--hover);
    --border-color-secondary--hover: var(--color-secondary--hover);
    --border-color-tertiary--hover: var(--color-tertiary--hover);
    --border-color-quaternary--hover: var(--color-quaternary--hover);

    --rounded-main-nav-cta: 0px;

    --color-cta-background: var(--color-secondary);
    --color-cta-text: var(--color-quaternary);
    --color-cta-text-hover: var(--color-primary);
    --font-nav-main-button: var(--font-p);
    --font-button-nav-main-button-font-family: var(--font-button-font-family);

    --color-exhibitor-item-border: var(--color-dividers-1);

    --news-selection-item-rounded: var(--rounded-news-card-tag);
    --news-selection-item-text-color: var(--color-news-card-tag-text);
    --news-selection-item-border-color: transparent;
    --news-selection-item-border-color-hover: transparent;
    --news-selection-item-bg-color: var(--color-news-card-tag-bg);
    --news-selection-item-bg-color-hover: var(--color-news-card-tag-bg-hover);
    --news-selection-item-text-color-hover: var(--color-news-card-tag-text-hover);
    --news-selection-item-margin: 0px;
    --news-selection-item-padding: 16px;
    --news-selection-item-padding-y: var(--news-selection-item-padding);
    --news-selection-item-font-size: var(--font-card-tag);
    --news-selection-item-font-weight: var(--font-weight-card-tag);

    /* news detail */
    --rounded-news-detail-tag: var(--rounded-news-card-tag);
    --padding-news-detail-tag: var(--news-selection-item-padding);
    --color-news-detail-tag: var(--news-selection-item-bg-color);
    --color-news-detail-tag-hover: var(--news-selection-item-bg-color-hover);

    /* card */
    --color-card-bg: white;
    --color-card-border: var(--color-dividers-1);
    --color-card-text: var(--color-primary-1);

    --color-potrait-card-role: var(--color-brand-body-1);
    --color-potrait-card-name: var(--color-potrait-card-role);

    /* news card */
    --rounded-news-card-tag: 8px;
    --rounded-news-card: var(--card-rounded);
    --border-news-card-border-width: 2px;
    --border-news-card-border-width-hover: var(--border-news-card-border-width);
    --color-news-card-bg: white;
    --color-news-card-border: var(--color-dividers-1);
    --color-news-card-border-hover: var(--color-news-card-border);
    --color-news-card-text: var(--color-body-1);
    --color-news-card-text-hover: var(--color-news-card-text);
    --color-news-card-tag-bg: var(--color-primary-1);
    --color-news-card-tag-text: var(--color-button-text);
    --color-news-card-tag-bg-hover: var(--color-news-card-tag-bg);
    --color-news-card-tag-text-hover: var(--color-news-card-tag-text);
    --font-news-card-tag-font-family: unset;
    --font-items-overview-title-font-weight: normal;
    --font-items-overview-body-font-weight: normal;

    --color-team-member-person-card-bg: white;
    --color-team-member-person-card-text: var(--color-body-1);

    --font-card-tag: var(--font-p-value);
    --font-weight-card-tag: bold;
    --font-footer-cta-button-text-weight: unset;
    --font-footer-text-heading-line-height: 1.0;
    --font-footer-text-heading-size: 18px;
    --font-footer-text-heading-text-weight: bold;
    --font-footer-organization-heading: 14px;
}